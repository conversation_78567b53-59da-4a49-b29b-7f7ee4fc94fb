/**
 * Script de test pour l'authentification Odoo 13 directe
 * Permet de tester la nouvelle implémentation sans lancer l'application complète
 */

const OdooAuth = require('./src/main/odoo-auth');
const log = require('electron-log');

// Configuration de test
const TEST_CONFIG = {
  serverUrl: 'http://localhost:8069', // Remplacer par votre serveur
  username: 'admin', // Remplacer par vos identifiants
  password: 'admin', // Remplacer par vos identifiants
  database: 'ligne-digitale' // Remplacer par votre base
};

/**
 * Test principal de l'authentification Odoo 13
 */
async function testOdoo13Authentication() {
  console.log('🧪 === DÉBUT DES TESTS AUTHENTIFICATION ODOO 13 ===');
  
  try {
    // Initialiser le module d'authentification
    const odooAuth = new OdooAuth();
    
    console.log(`📡 Test sur serveur: ${TEST_CONFIG.serverUrl}`);
    console.log(`👤 Utilisateur: ${TEST_CONFIG.username}`);
    console.log(`🗄️ Base de données: ${TEST_CONFIG.database}`);
    
    // Test 1: Authentification directe
    console.log('\n🔍 Test 1: Authentification via API officielle');
    const authResult = await odooAuth.authenticateOdoo13Direct(
      TEST_CONFIG.username,
      TEST_CONFIG.password,
      TEST_CONFIG.serverUrl,
      TEST_CONFIG.database
    );
    
    if (authResult.success) {
      console.log('✅ Test 1 RÉUSSI - Authentification directe');
      console.log(`   Session ID: ${authResult.sessionId.substring(0, 12)}...`);
      console.log(`   Utilisateur: ${authResult.name}`);
      console.log(`   UID: ${authResult.userId}`);
      
      // Test 2: Vérification de session
      console.log('\n🔍 Test 2: Vérification de session');
      const sessionCheck = await odooAuth.verifyOdoo13Session(
        TEST_CONFIG.serverUrl,
        authResult.sessionId
      );
      
      if (sessionCheck.valid) {
        console.log('✅ Test 2 RÉUSSI - Session valide');
        console.log(`   UID vérifié: ${sessionCheck.uid}`);
        console.log(`   Nom vérifié: ${sessionCheck.name}`);
      } else {
        console.log('❌ Test 2 ÉCHOUÉ - Session invalide');
        console.log(`   Erreur: ${sessionCheck.error}`);
      }
      
      // Test 3: Déconnexion
      console.log('\n🔍 Test 3: Déconnexion');
      const logoutResult = await odooAuth.logout(
        TEST_CONFIG.serverUrl,
        authResult.sessionId
      );
      
      if (logoutResult) {
        console.log('✅ Test 3 RÉUSSI - Déconnexion');
      } else {
        console.log('⚠️ Test 3 PARTIEL - Déconnexion avec erreurs');
      }
      
    } else {
      console.log('❌ Test 1 ÉCHOUÉ - Authentification directe');
      console.log(`   Erreur: ${authResult.error}`);
    }
    
  } catch (error) {
    console.log('❌ ERREUR CRITIQUE DANS LES TESTS');
    console.log(`   Message: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }
  
  console.log('\n🧪 === FIN DES TESTS ===');
}

/**
 * Test de parsing des cookies
 */
function testCookieParsing() {
  console.log('\n🍪 Test de parsing des cookies');
  
  const odooAuth = new OdooAuth();
  
  // Simuler des headers set-cookie
  const mockSetCookieHeaders = [
    'session_id=abc123def456; Path=/; HttpOnly',
    'frontend_lang=fr_FR; Path=/',
    'other_cookie=value; Path=/; Secure'
  ];
  
  const parsedCookies = odooAuth.parseOdoo13Cookies(mockSetCookieHeaders);
  
  console.log('Cookies parsés:', parsedCookies);
  
  if (parsedCookies.session_id === 'abc123def456') {
    console.log('✅ Parsing session_id correct');
  } else {
    console.log('❌ Parsing session_id incorrect');
  }
  
  if (parsedCookies.frontend_lang === 'fr_FR') {
    console.log('✅ Parsing frontend_lang correct');
  } else {
    console.log('❌ Parsing frontend_lang incorrect');
  }
}

/**
 * Afficher les informations de configuration
 */
function showTestInfo() {
  console.log('📋 === INFORMATIONS DE TEST ===');
  console.log('Pour utiliser ce script de test:');
  console.log('1. Modifiez les constantes TEST_CONFIG avec vos paramètres');
  console.log('2. Assurez-vous que votre serveur Odoo est accessible');
  console.log('3. Lancez: node test-odoo13-auth.js');
  console.log('');
  console.log('Configuration actuelle:');
  console.log(`  Serveur: ${TEST_CONFIG.serverUrl}`);
  console.log(`  Utilisateur: ${TEST_CONFIG.username}`);
  console.log(`  Base: ${TEST_CONFIG.database}`);
  console.log('');
}

// Exécution des tests
if (require.main === module) {
  showTestInfo();
  testCookieParsing();
  testOdoo13Authentication();
}

module.exports = {
  testOdoo13Authentication,
  testCookieParsing,
  TEST_CONFIG
};
