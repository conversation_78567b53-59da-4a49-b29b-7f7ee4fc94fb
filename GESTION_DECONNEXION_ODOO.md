# 🚪 Gestion des Déconnexions Odoo - Redirection vers Interface de Connexion

## 🎯 Problème Résolu

**AVANT** : Quand l'utilisateur se déconnectait via l'interface Odoo, il était redirigé vers la page de login d'Odoo (`/web/login`).

**MAINTENANT** : La déconnexion redirige automatiquement vers l'interface de connexion de l'application Electron.

## ✅ Solution Implémentée

### 1. Interception des Navigations
```javascript
// Intercepter les navigations vers /web/login
this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
  if (navigationUrl.includes('/web/login') || navigationUrl.includes('/web/session/logout')) {
    event.preventDefault(); // Empêcher la navigation
    this.handleOdooLogout(); // Rediriger vers notre interface
  }
});
```

### 2. Interception Côté Client (JavaScript)
```javascript
// Intercepter les clics sur les liens de déconnexion
document.addEventListener('click', function(event) {
  const href = target.href || target.closest('a')?.href;
  if (href && href.includes('logout')) {
    event.preventDefault();
    window.electronAPI.notifyLogout(); // Notifier Electron
  }
}, true);
```

### 3. Surveillance des Redirections Automatiques
```javascript
// Intercepter les redirections via history API
history.pushState = function(state, title, url) {
  if (url && url.includes('/web/login')) {
    window.electronAPI.notifyLogout();
    return;
  }
  return originalPushState.apply(this, arguments);
};
```

## 🔧 Mécanismes d'Interception

### Niveau 1: Navigation Electron
- **Événement** : `will-navigate`
- **Détection** : URLs contenant `/web/login` ou `/web/session/logout`
- **Action** : `event.preventDefault()` + redirection

### Niveau 2: Injection JavaScript
- **Événement** : `click` sur liens de déconnexion
- **Détection** : `href` contenant `logout`
- **Action** : `event.preventDefault()` + notification IPC

### Niveau 3: History API
- **Événement** : `pushState` / `replaceState`
- **Détection** : URLs contenant `/web/login`
- **Action** : Interception + notification IPC

### Niveau 4: Surveillance URL
- **Méthode** : Polling toutes les secondes
- **Détection** : Changement d'URL vers `/web/login`
- **Action** : Notification IPC

## 📋 Flux de Déconnexion

```
1. Utilisateur clique "Déconnexion" dans Odoo
   ↓
2. Interception du clic (JavaScript injecté)
   ↓
3. Prevention de la navigation par défaut
   ↓
4. Notification à Electron via IPC
   ↓
5. Nettoyage de la session Electron
   ↓
6. Redirection vers interface de connexion
   ↓
7. ✅ Utilisateur sur l'écran de connexion de l'app
```

## 🔧 Fichiers Modifiés

### `src/main/window-manager.js`
- ✅ `handleOdooLogout()` - Gestion de la déconnexion
- ✅ `injectLogoutInterceptor()` - Injection JavaScript
- ✅ `setupOdooEventHandlers()` - Interception navigations

### `preload.js`
- ✅ `notifyLogout()` - API pour notification de déconnexion

### `main.js`
- ✅ `handle-odoo-logout` - Gestionnaire IPC

## 🚀 Fonctionnalités

### Interception Multiple
- **Clics directs** sur boutons de déconnexion
- **Redirections automatiques** via JavaScript
- **Navigations** via barre d'adresse
- **History API** (pushState/replaceState)

### Nettoyage Complet
```javascript
// Nettoyage de la session Electron
await ses.clearStorageData({
  storages: ['cookies', 'localstorage', 'sessionstorage', 'cachestorage']
});
```

### Fallbacks Robustes
- Si l'interception JavaScript échoue → Navigation Electron
- Si la navigation Electron échoue → Surveillance URL
- Si tout échoue → Redirection forcée

## 📊 Logs de Validation

### Déconnexion Détectée
```
🚪 Déconnexion Odoo détectée - redirection vers interface de connexion
🚪 === GESTION DÉCONNEXION ODOO ===
🔄 Redirection vers l'interface de connexion de l'application
✅ Redirection vers interface de connexion terminée
```

### Interception JavaScript
```
🔧 Injection de l'intercepteur de déconnexion Odoo
🚪 Clic de déconnexion détecté: /web/session/logout
✅ Intercepteur de déconnexion installé
```

## 🎯 Résultats

### ❌ Comportement Précédent
1. Clic déconnexion → Page login Odoo
2. Utilisateur bloqué sur `/web/login`
3. Nécessité de fermer/relancer l'app

### ✅ Nouveau Comportement
1. Clic déconnexion → Interception automatique
2. Nettoyage session → Redirection fluide
3. Interface de connexion → Nouvelle authentification

## 🔍 Tests et Validation

### Test Manuel
1. Se connecter à Odoo via l'application
2. Cliquer sur "Déconnexion" dans l'interface Odoo
3. ✅ Vérifier la redirection vers l'interface de connexion

### Logs à Surveiller
- 🚪 Détection de déconnexion
- 🔄 Redirection en cours
- ✅ Succès de la redirection

## 🛠️ Maintenance

### Points de Contrôle
- Vérifier l'injection JavaScript après mise à jour Odoo
- Tester les différents types de déconnexion
- Valider le nettoyage de session

### Debugging
- Logs avec emojis pour identification rapide
- Fallbacks multiples en cas d'échec
- Messages d'erreur détaillés

---

**🎯 OBJECTIF ATTEINT** : Les déconnexions Odoo redirigent maintenant vers l'interface de connexion de l'application au lieu de la page de login Odoo.
