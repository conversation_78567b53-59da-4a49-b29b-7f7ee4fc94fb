/**
 * Module d'authentification Odoo 13 pour l'application Edara
 * Implémente la connexion directe via l'API officielle /web/session/authenticate
 * SOLUTION TECHNIQUE SPÉCIALISÉE POUR ODOO 13
 */

const axios = require('axios');
const log = require('electron-log');

class OdooAuth {
  constructor() {
    this.defaultDatabase = 'ligne-digitale';
    this.authTimeout = 15000; // 15 secondes pour Odoo 13

    // Configuration axios OBLIGATOIRE pour Odoo 13
    this.axiosInstance = axios.create({
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      withCredentials: true, // CRUCIAL pour Odoo 13
      validateStatus: function (status) {
        return status >= 200 && status < 500;
      }
    });
  }

  /**
   * Authentification Odoo 13 via l'API officielle /web/session/authenticate
   * IMPLÉMENTATION SPÉCIALISÉE POUR CONNEXION DIRECTE SANS PAGE DE LOGIN
   * @param {string} username - Nom d'utilisateur ou email
   * @param {string} password - Mot de passe
   * @param {string} serverUrl - URL du serveur Odoo
   * @param {string} database - Nom de la base de données (optionnel)
   * @returns {Promise<Object>} - Résultat de l'authentification
   */
  async authenticate(username, password, serverUrl, database = null) {
    try {
      log.info(`✅ Authentification via endpoint officiel: /web/session/authenticate`);
      log.info(`Tentative d'authentification pour ${username} sur ${serverUrl}`);

      const dbName = database || this.defaultDatabase;

      // Payload d'authentification selon spécifications Odoo 13
      const authPayload = {
        jsonrpc: "2.0",
        method: "call",
        params: {
          db: dbName,
          login: username,
          password: password,
          context: {
            lang: "fr_FR",
            tz: "Europe/Paris"
          }
        },
        id: Math.floor(Math.random() * 1000000)
      };

      log.info(`Payload d'authentification préparé pour la base: ${dbName}`);

      // Effectuer l'authentification via l'API officielle
      const authResponse = await this.axiosInstance.post(
        `${serverUrl}/web/session/authenticate`,
        authPayload
      );

      log.info(`✅ Réponse d'authentification - Status: ${authResponse.status}`);

      // Traiter la réponse d'authentification
      const authResult = this.parseOdoo13AuthResponse(authResponse);

      if (authResult.success) {
        log.info(`✅ Authentification réussie pour ${username} (UID: ${authResult.userId})`);

        // Vérifier la session Odoo 13
        const sessionVerification = await this.verifyOdoo13Session(serverUrl, authResult.sessionId);

        if (sessionVerification.valid) {
          log.info(`✅ Session Odoo 13 vérifiée - UID: ${sessionVerification.uid}`);

          return {
            success: true,
            userId: authResult.userId,
            name: authResult.name || username,
            sessionId: authResult.sessionId,
            dbName: dbName,
            serverUrl: serverUrl,
            sessionData: sessionVerification
          };
        } else {
          log.error(`❌ Session invalide après authentification`);
          return {
            success: false,
            error: 'Session invalide après authentification'
          };
        }
      } else {
        log.error(`❌ Échec de l'authentification: ${authResult.error}`);
        return {
          success: false,
          error: authResult.error || 'Identifiants invalides'
        };
      }

    } catch (error) {
      log.error(`❌ Erreur lors de l'authentification:`, error);

      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: 'Impossible de se connecter au serveur Odoo'
        };
      } else if (error.code === 'ETIMEDOUT') {
        return {
          success: false,
          error: 'Timeout de connexion au serveur'
        };
      } else {
        return {
          success: false,
          error: error.message || 'Erreur inconnue lors de l\'authentification'
        };
      }
    }
  }

  /**
   * Parser la réponse d'authentification Odoo 13
   * TRAITEMENT SPÉCIALISÉ POUR L'API /web/session/authenticate
   * @param {Object} authResponse - Réponse axios de l'authentification
   * @returns {Object} - Résultat parsé avec session_id
   */
  parseOdoo13AuthResponse(authResponse) {
    try {
      const responseData = authResponse.data;

      // Vérifier la structure de réponse JSON-RPC
      if (!responseData || responseData.error) {
        const errorMsg = responseData?.error?.data?.message || 'Authentification échouée';
        log.error(`❌ Erreur dans la réponse: ${errorMsg}`);
        return {
          success: false,
          error: errorMsg
        };
      }

      const sessionResult = responseData.result;

      // Vérifier la présence de l'UID (authentification réussie)
      if (!sessionResult || !sessionResult.uid || sessionResult.uid <= 0) {
        log.error(`❌ UID invalide ou manquant dans la réponse`);
        return {
          success: false,
          error: 'Identifiants invalides'
        };
      }

      // Extraire les cookies depuis les headers set-cookie
      const setCookieHeaders = authResponse.headers['set-cookie'] || [];
      log.info(`✅ Cookies reçus: ${setCookieHeaders.length} cookies`);

      const sessionCookies = this.parseOdoo13Cookies(setCookieHeaders);

      // Session ID peut être dans les cookies OU dans la réponse
      const sessionId = sessionCookies.session_id ||
                       sessionResult.session_id ||
                       this.generateSessionId();

      log.info(`✅ Cookie parsé: session_id = ${sessionId.substring(0, 12)}...`);

      return {
        success: true,
        userId: sessionResult.uid,
        name: sessionResult.name || sessionResult.username,
        sessionId: sessionId,
        cookies: sessionCookies,
        sessionData: sessionResult
      };

    } catch (error) {
      log.error('❌ Erreur lors du parsing de la réponse Odoo 13:', error);
      return {
        success: false,
        error: 'Erreur lors du traitement de la réponse du serveur'
      };
    }
  }

  /**
   * Parser les cookies Odoo 13 depuis les headers set-cookie
   * EXTRACTION SPÉCIALISÉE POUR ODOO 13
   * @param {Array} setCookieHeaders - Headers set-cookie
   * @returns {Object} - Cookies parsés
   */
  parseOdoo13Cookies(setCookieHeaders) {
    const cookies = {};

    for (const cookieHeader of setCookieHeaders) {
      // Parser chaque cookie
      const cookieParts = cookieHeader.split(';')[0].split('=');
      if (cookieParts.length === 2) {
        const name = cookieParts[0].trim();
        const value = cookieParts[1].trim();
        cookies[name] = value;

        if (name === 'session_id') {
          log.info(`✅ Session ID trouvé dans les cookies: ${value.substring(0, 12)}...`);
        }
      }
    }

    return cookies;
  }

  /**
   * Vérifier la session Odoo 13 avec l'endpoint officiel
   * VÉRIFICATION SPÉCIALISÉE POUR ODOO 13
   * @param {string} serverUrl - URL du serveur
   * @param {string} sessionId - ID de session à vérifier
   * @returns {Promise<Object>} - Résultat de la vérification
   */
  async verifyOdoo13Session(serverUrl, sessionId) {
    try {
      log.info('🔍 Vérification de la session Odoo 13...');

      const verifyResponse = await this.axiosInstance.post(
        `${serverUrl}/web/session/get_session_info`,
        {
          jsonrpc: "2.0",
          method: "call",
          params: {},
          id: Math.floor(Math.random() * 1000000)
        },
        {
          headers: {
            'Cookie': `session_id=${sessionId}`
          }
        }
      );

      const sessionInfo = verifyResponse.data.result;

      if (sessionInfo && sessionInfo.uid && sessionInfo.uid > 0) {
        log.info(`✅ Session valide - UID: ${sessionInfo.uid}, Utilisateur: ${sessionInfo.name}`);
        return {
          valid: true,
          uid: sessionInfo.uid,
          name: sessionInfo.name,
          db: sessionInfo.db,
          sessionInfo: sessionInfo
        };
      } else {
        log.error(`❌ Session invalide - pas d'UID valide`);
        return {
          valid: false,
          error: 'Session invalide'
        };
      }

    } catch (error) {
      log.error('❌ Erreur lors de la vérification de session:', error.message);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Générer un ID de session temporaire
   * @returns {string} - ID de session généré
   */
  generateSessionId() {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2);
    return `edara_${timestamp}_${random}`;
  }

  /**
   * Méthode de compatibilité - utilise verifyOdoo13Session
   * @deprecated Utiliser verifyOdoo13Session à la place
   * @param {string} serverUrl - URL du serveur
   * @param {string} sessionId - ID de session
   * @returns {Promise<boolean>} - true si la session est valide
   */
  async verifySession(serverUrl, sessionId) {
    const result = await this.verifyOdoo13Session(serverUrl, sessionId);
    return result.valid;
  }

  /**
   * Déconnecter un utilisateur d'Odoo 13
   * @param {string} serverUrl - URL du serveur
   * @param {string} sessionId - ID de session
   * @returns {Promise<boolean>} - true si la déconnexion a réussi
   */
  async logout(serverUrl, sessionId) {
    try {
      log.info('🚪 Déconnexion de l\'utilisateur...');

      await this.axiosInstance.post(
        `${serverUrl}/web/session/destroy`,
        {
          jsonrpc: '2.0',
          method: 'call',
          params: {}
        },
        {
          headers: {
            'Cookie': `session_id=${sessionId}`
          },
          timeout: 5000
        }
      );

      log.info('✅ Déconnexion réussie');
      return true;

    } catch (error) {
      log.warn('⚠️ Erreur lors de la déconnexion:', error.message);
      return false;
    }
  }

  /**
   * Fonction principale d'authentification Odoo 13 avec connexion directe
   * POINT D'ENTRÉE PRINCIPAL POUR LA CONNEXION TRANSPARENTE
   * @param {string} username - Nom d'utilisateur ou email
   * @param {string} password - Mot de passe
   * @param {string} serverUrl - URL du serveur Odoo
   * @param {string} database - Nom de la base de données (optionnel)
   * @returns {Promise<Object>} - Résultat complet de l'authentification
   */
  async authenticateOdoo13Direct(username, password, serverUrl, database = null) {
    try {
      log.info('🚀 === DÉBUT AUTHENTIFICATION ODOO 13 DIRECTE ===');
      log.info(`📡 Serveur: ${serverUrl}`);
      log.info(`👤 Utilisateur: ${username}`);
      log.info(`🗄️ Base de données: ${database || this.defaultDatabase}`);

      // Étape 1: Authentification via l'API officielle
      const authResult = await this.authenticate(username, password, serverUrl, database);

      if (!authResult.success) {
        log.error('❌ Échec de l\'authentification');
        return authResult;
      }

      log.info('✅ === AUTHENTIFICATION ODOO 13 RÉUSSIE ===');
      log.info(`🎯 Session ID: ${authResult.sessionId.substring(0, 12)}...`);
      log.info(`👤 Utilisateur connecté: ${authResult.name}`);
      log.info(`🆔 UID: ${authResult.userId}`);

      return authResult;

    } catch (error) {
      log.error('❌ === ÉCHEC AUTHENTIFICATION ODOO 13 ===');
      log.error('Erreur:', error.message);

      return {
        success: false,
        error: error.message || 'Erreur inconnue lors de l\'authentification'
      };
    }
  }
}

module.exports = OdooAuth;
