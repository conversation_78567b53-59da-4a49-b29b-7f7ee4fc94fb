/**
 * Gestionnaire de connexions pour l'application Edara
 * Gère la connexion aux serveurs Odoo locaux et distants
 */

const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const log = require('electron-log');

class ConnectionManager {
  constructor() {
    this.localServers = [];
    this.remoteServerUrl = 'https://edara.ligne-digitale.com';
    this.localPort = 8069;
    this.connectionTimeout = 5000; // 5 secondes
  }

  /**
   * Charger la configuration des serveurs depuis le fichier serveur_ip.txt
   */
  async loadServerConfig() {
    try {
      log.info('Chargement de la configuration des serveurs...');
      
      const configPath = path.join(process.cwd(), 'serveur_ip.txt');
      const fileContent = await fs.readFile(configPath, 'utf8');
      
      // Parser le contenu du fichier
      this.localServers = fileContent
        .split('\n')
        .map(line => line.trim())
        .filter(line => line && !line.startsWith('#'))
        .map(ip => `http://${ip}:${this.localPort}`);
      
      log.info(`Configuration chargée: ${this.localServers.length} serveurs locaux trouvés`);
      log.debug('Serveurs locaux:', this.localServers);
      
    } catch (error) {
      log.warn('Impossible de charger le fichier serveur_ip.txt, utilisation des valeurs par défaut');
      
      // Valeurs par défaut si le fichier n'existe pas
      this.localServers = [
        'http://**************:8069',
        'http://************:8069',
        'http://************:8069'
      ];
    }
  }

  /**
   * Tester la connexion vers un serveur
   * @param {string} serverUrl - URL du serveur à tester
   * @returns {Promise<boolean>} - true si le serveur est accessible
   */
  async testConnection(serverUrl) {
    try {
      log.debug(`Test de connexion vers: ${serverUrl}`);
      
      const response = await axios.get(`${serverUrl}/web/database/selector`, {
        timeout: this.connectionTimeout,
        validateStatus: (status) => status < 500 // Accepter les codes 2xx, 3xx et 4xx
      });
      
      log.debug(`Connexion réussie vers ${serverUrl} (status: ${response.status})`);
      return true;
      
    } catch (error) {
      log.debug(`Connexion échouée vers ${serverUrl}: ${error.message}`);
      return false;
    }
  }

  /**
   * Trouver le premier serveur local disponible
   * @returns {Promise<string|null>} - URL du serveur disponible ou null
   */
  async findAvailableLocalServer() {
    log.info('Recherche d\'un serveur local disponible...');
    
    for (const serverUrl of this.localServers) {
      const isAvailable = await this.testConnection(serverUrl);
      
      if (isAvailable) {
        log.info(`Serveur local trouvé: ${serverUrl}`);
        return serverUrl;
      }
    }
    
    log.warn('Aucun serveur local disponible');
    return null;
  }

  /**
   * Tester la connexion au serveur distant
   * @returns {Promise<boolean>} - true si le serveur distant est accessible
   */
  async testRemoteConnection() {
    log.info('Test de connexion au serveur distant...');
    return await this.testConnection(this.remoteServerUrl);
  }

  /**
   * Obtenir l'URL du serveur distant
   * @returns {string} - URL du serveur distant
   */
  getRemoteServerUrl() {
    return this.remoteServerUrl;
  }

  /**
   * Obtenir la liste de tous les serveurs disponibles (locaux + distant)
   * @returns {Promise<Array>} - Liste des serveurs avec leur statut
   */
  async getAvailableServers() {
    log.info('Vérification de tous les serveurs...');
    
    const servers = [];
    
    // Tester les serveurs locaux
    for (const serverUrl of this.localServers) {
      const isAvailable = await this.testConnection(serverUrl);
      servers.push({
        url: serverUrl,
        type: 'local',
        available: isAvailable
      });
    }
    
    // Tester le serveur distant
    const remoteAvailable = await this.testRemoteConnection();
    servers.push({
      url: this.remoteServerUrl,
      type: 'remote',
      available: remoteAvailable
    });
    
    const availableCount = servers.filter(s => s.available).length;
    log.info(`${availableCount}/${servers.length} serveurs disponibles`);
    
    return servers;
  }

  /**
   * Obtenir le meilleur serveur disponible selon la stratégie de priorité
   * @returns {Promise<string|null>} - URL du meilleur serveur ou null
   */
  async getBestAvailableServer() {
    log.info('Recherche du meilleur serveur disponible...');
    
    // 1. Essayer d'abord les serveurs locaux
    const localServer = await this.findAvailableLocalServer();
    if (localServer) {
      return localServer;
    }
    
    // 2. Fallback vers le serveur distant
    const remoteAvailable = await this.testRemoteConnection();
    if (remoteAvailable) {
      log.info(`Utilisation du serveur distant: ${this.remoteServerUrl}`);
      return this.remoteServerUrl;
    }
    
    log.error('Aucun serveur disponible (local ou distant)');
    return null;
  }

  /**
   * Configurer un timeout personnalisé pour les connexions
   * @param {number} timeout - Timeout en millisecondes
   */
  setConnectionTimeout(timeout) {
    this.connectionTimeout = timeout;
    log.info(`Timeout de connexion défini à ${timeout}ms`);
  }

  /**
   * Ajouter un serveur local à la liste
   * @param {string} ip - Adresse IP du serveur
   */
  addLocalServer(ip) {
    const serverUrl = `http://${ip}:${this.localPort}`;
    if (!this.localServers.includes(serverUrl)) {
      this.localServers.push(serverUrl);
      log.info(`Serveur local ajouté: ${serverUrl}`);
    }
  }

  /**
   * Supprimer un serveur local de la liste
   * @param {string} ip - Adresse IP du serveur à supprimer
   */
  removeLocalServer(ip) {
    const serverUrl = `http://${ip}:${this.localPort}`;
    const index = this.localServers.indexOf(serverUrl);
    if (index > -1) {
      this.localServers.splice(index, 1);
      log.info(`Serveur local supprimé: ${serverUrl}`);
    }
  }
}

module.exports = ConnectionManager;
