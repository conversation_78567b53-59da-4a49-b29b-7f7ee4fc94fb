/* Styles pour la page de connexion personnalisée Edara - Version finale exacte */

/* Variables CSS pour le thème sombre (par défaut) */
:root {
  --background-color: #181818;
  --text-color: #F8F9FA;
  --secondary-text-color: #DEE2E6;
  --button-background: #0178D5;
  --button-hover: #0064B5;
  --form-background: #1F1E1E;
  --input-background: rgba(255, 255, 255, 0.05);
  --input-border: rgba(255, 255, 255, 0.3);
  --input-focus: #121111;
  --input-text: #F8F9FA;
  --input-placeholder: rgba(255, 255, 255, 0.4);
  --error-color: #e74c3c;
  --success-color: #2ecc71;
  --separator-color: rgba(255, 255, 255, 0.1);
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  --toggle-icon-color: #DEE2E6;
  --dropdown-background: #1F1E1E;
  --dropdown-text: #FFFFFF;
}

/* Variables CSS pour le thème clair */
/* Thème clair basé sur les préférences système */
@media (prefers-color-scheme: light) {
  :root {
    --background-color: #F5F5F5;
    --text-color: #212529;
    --secondary-text-color: #6C757D;
    --button-background: #0178D5;
    --button-hover: #0064B5;
    --form-background: #FFFFFF;
    --input-background: #F8F9FA;
    --input-border: rgba(0, 0, 0, 0.2);
    --input-focus: #E9ECEF;
    --input-text: #212529;
    --input-placeholder: rgba(0, 0, 0, 0.4);
    --error-color: #e74c3c;
    --success-color: #2ecc71;
    --separator-color: rgba(0, 0, 0, 0.1);
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --toggle-icon-color: #6C757D;
    --dropdown-background: #FFFFFF;
    --dropdown-text: #212529;
  }
}

/* Classes pour le basculement manuel du thème */
.light-theme {
  --background-color: #F5F5F5;
  --text-color: #212529;
  --secondary-text-color: #6C757D;
  --button-background: #0178D5;
  --button-hover: #0064B5;
  --form-background: #FFFFFF;
  --input-background: #F8F9FA;
  --input-border: rgba(0, 0, 0, 0.2);
  --input-focus: #E9ECEF;
  --input-text: #212529;
  --input-placeholder: rgba(0, 0, 0, 0.4);
  --error-color: #e74c3c;
  --success-color: #2ecc71;
  --separator-color: rgba(0, 0, 0, 0.1);
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  --toggle-icon-color: #6C757D;
  --dropdown-background: #FFFFFF;
  --dropdown-text: #212529;
}

.dark-theme {
  --background-color: #181818;
  --text-color: #F8F9FA;
  --secondary-text-color: #DEE2E6;
  --button-background: #0178D5;
  --button-hover: #0064B5;
  --form-background: #1F1E1E;
  --input-background: rgba(255, 255, 255, 0.05);
  --input-border: rgba(255, 255, 255, 0.3);
  --input-focus: #121111;
  --input-text: #F8F9FA;
  --input-placeholder: rgba(255, 255, 255, 0.4);
  --error-color: #e74c3c;
  --success-color: #2ecc71;
  --separator-color: rgba(255, 255, 255, 0.1);
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  --toggle-icon-color: #DEE2E6;
  --dropdown-background: #1F1E1E;
  --dropdown-text: #FFFFFF;
}

/* Réinitialisation des styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden;
}

/* Conteneur principal */
.edara-container {
  padding: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--background-color);
  gap: 80px; /* Espacement horizontal entre les éléments */
  transition: background-color 0.3s ease;
}

/* Section gauche (formulaire) */
.left-section {
  flex: 0 0 380px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30px;
  position: relative;
  background-color: var(--form-background);
  border-radius: 7px;
  box-shadow: var(--box-shadow);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

/* Section droite (illustration) */
.right-section {
  flex: 0 0 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

/* Conteneur du formulaire */
.login-form-container {
  width: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
}

/* Logo */
.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  margin-top: 10px;
}

#edara-login-logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
}

/* Séparateur */
.separator {
  width: 100%;
  height: 1px;
  background-color: var(--separator-color);
  margin-bottom: 20px;
}

/* Titre */
.login-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 25px;
  text-align: center;
  color: var(--text-color);
}

/* Formulaire */
.edara-login-form {
  width: 100%;
}

/* Groupes de formulaire */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 13px;
  margin-bottom: 5px;
  color: var(--text-color);
  font-weight: 400;
}

/* Champs de saisie */
.form-control {
  width: 100%;
  padding: 10px 12px;
  background-color: var(--input-background);
  border: 1px solid var(--input-border);
  border-radius: 6px;
  color: var(--input-text);
  font-size: 14px;
  height: 38px;
  transition: all 0.3s ease;
}

.form-control::placeholder {
  color: var(--input-placeholder);
  font-size: 13px;
}

.form-control:focus {
  outline: none;
  background-color: var(--input-focus);
  border: 1px solid var(--button-background);
}

/* Select spécifique */
select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 30px;
}

[data-theme="light"] select.form-control {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Conteneur de mot de passe */
.password-container {
  position: relative;
}

.password-container .form-control {
  padding-right: 40px;
}

.toggle-password {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--secondary-text-color);
  cursor: pointer;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  padding: 0;
  width: 24px;
  height: 24px;
}

.toggle-password:focus {
  outline: none;
}

.toggle-password:hover {
  opacity: 1;
}

/* Bouton de connexion */
.login-button {
  width: 100%;
  padding: 10px 15px;
  background-color: var(--button-background);
  color: #ffffff;
  border: none;
  border-radius: 4px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  height: 40px;
  margin-top: 20px;
  transition: background-color 0.2s ease, transform 0.1s ease, opacity 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.login-button:hover:not(:disabled) {
  background-color: var(--button-hover);
}

.login-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.login-button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

/* Animation de chargement pour le bouton */
.login-button.connecting::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-sweep 1.5s infinite;
}

@keyframes loading-sweep {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Messages d'erreur et de succès */
.error-message, .success-message {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Conteneur d'illustration */
.illustration-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

}

.login-illustration {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  opacity: 1;
}

/* Styles spécifiques pour le groupe d'instance */
.instance-group label {
  font-weight: 500;
}

.instance-select {
  font-weight: 500;
  position: relative;
  z-index: 10;
  top: 0;
  left: 0;
  transform: translateY(2px); /* Déplace le select légèrement vers le bas */
}

.instance-option {
  font-weight: 500;
  background-color: var(--dropdown-background);
  padding: 8px 12px;
  color: var(--dropdown-text);
}

/* Style pour le select au focus */
select.form-control:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  z-index: 20;
}

/* Ajustement de la position du menu déroulant */
select.form-control {
  margin-bottom: 2px; /* Crée un petit espace sous le select */
}

select.form-control option {
  background-color: var(--dropdown-background);
  color: var(--dropdown-text);
  padding: 10px;
  margin: 5px;
  position: relative;
  top: 2px; /* Ajustez cette valeur pour déplacer les options vers le bas */
  left: 10px; /* Ajustez cette valeur pour déplacer les options vers la droite */
}

/* Responsive design */
@media (max-width: 992px) {
  .edara-login-container {
    width: 95%;
    max-width: 800px;
  }

  .left-section {
    flex: 0 0 340px;
  }
}

@media (max-width: 768px) {
  .edara-content-wrapper {
    flex-direction: column;
    height: auto;
  }

  .left-section {
    flex: none;
    width: 100%;
    order: 2;
    padding: 30px 20px;
  }

  .right-section {
    flex: none;
    width: 100%;
    order: 1;
    height: 200px;
  }

  .login-form-container {
    padding: 0;
  }

  .illustration-container {
    padding: 20px;
  }

  .login-illustration {
    max-height: 160px;
  }
}

@media (max-width: 480px) {
  .edara-login-container {
    width: 100%;
    height: 100%;
  }

  .edara-content-wrapper {
    border-radius: 0;
    box-shadow: none;
  }

  .left-section {
    padding: 20px;
  }

  .right-section {
    height: 150px;
  }

  .login-form-container {
    max-width: 100%;
  }

  #edara-login-logo {
    width: 70px;
    height: 70px;
  }

  .login-title {
    font-size: 20px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-control, .login-button {
    height: 38px;
  }
}
