<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edara ERP - Connexion</title>
    <link rel="stylesheet" href="style-final.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Styles pour le bouton de basculement de thème */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 20px;
            cursor: pointer;
            z-index: 100;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .theme-toggle:hover {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- Bouton de basculement de thème -->
    <button id="theme-toggle" class="theme-toggle">
        <i class="fas fa-moon"></i>
    </button>

    <div class="edara-container">
        <!-- Section gauche : formulaire -->
        <div class="left-section">
            <!-- Cadre secondaire contenant le formulaire -->
            <div class="login-form-container">
                <!-- Logo -->
                <div class="logo-container">
                    <img id="edara-login-logo" src="img/logo-edara-claire.png" alt="Edara Logo">
                </div>

                <!-- Séparateur -->
                <div class="separator"></div>

                <!-- Titre -->
                <h1 class="login-title">Connexion</h1>

                <!-- Formulaire -->
                <form class="edara-login-form">
                    <!-- Instance -->
                    <div class="form-group instance-group">
                        <label for="instance">Instance</label>
                        <select id="instance" class="form-control instance-select">
                            <option value="local" class="instance-option">Connexion Local</option>
                            <option value="distance" selected class="instance-option">Connexion Distance</option>
                        </select>
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="text" id="email" class="form-control" placeholder="Nom d'utilisateur ou email">
                    </div>

                    <!-- Mot de passe -->
                    <div class="form-group">
                        <label for="password">Mot de passe</label>
                        <div class="password-container">
                            <input type="password" id="password" class="form-control" placeholder="Votre mot de passe">
                            <button type="button" id="toggle-password" class="toggle-password">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bouton de connexion -->
                    <button type="submit" class="login-button">Se connecter</button>
                </form>
            </div>
        </div>

        <!-- Section droite : illustration -->
        <div class="right-section">
            <div class="illustration-container">
                <img src="img/edara_illustration.svg" alt="Edara Illustration" class="login-illustration">
            </div>
        </div>
    </div>

    <!-- Script de gestion de la connexion Electron -->
    <script src="src/renderer/login-handler.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Afficher/masquer le mot de passe
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Changer l'icône
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Variables pour la gestion du thème
            const themeToggleBtn = document.getElementById('theme-toggle');
            let userTheme = localStorage.getItem('theme');
            let systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';

            // Fonction pour définir le thème
            function setTheme(theme) {
                if (theme === 'dark') {
                    document.documentElement.classList.add('dark-theme');
                    document.documentElement.classList.remove('light-theme');
                    themeToggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
                } else {
                    document.documentElement.classList.add('light-theme');
                    document.documentElement.classList.remove('dark-theme');
                    themeToggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
                }

                // Mettre à jour le logo en fonction du thème
                const logoElement = document.getElementById('edara-login-logo');
                if (logoElement) {
                    logoElement.src = theme === 'dark'
                        ? 'img/logo-edara-claire.png'
                        : 'img/logo-edara-noire.png';
                }

                // Sauvegarder le thème choisi par l'utilisateur
                localStorage.setItem('theme', theme);
            }

            // Fonction pour détecter le thème du système
            function detectColorScheme() {
                const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
                systemTheme = isDarkMode ? 'dark' : 'light';

                // Appliquer le thème choisi par l'utilisateur ou le thème du système
                if (userTheme) {
                    setTheme(userTheme);
                } else {
                    setTheme(systemTheme);
                }
            }

            // Basculer le thème manuellement
            themeToggleBtn.addEventListener('click', function() {
                userTheme = document.documentElement.classList.contains('dark-theme') ? 'light' : 'dark';
                setTheme(userTheme);
            });

            // Détecter le thème initial
            detectColorScheme();

            // Écouter les changements de thème du système
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                if (!userTheme) {
                    detectColorScheme();
                }
            });
        });
    </script>
</body>
</html>
